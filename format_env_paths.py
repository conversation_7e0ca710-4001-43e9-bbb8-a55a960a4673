#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量路径整理工具
将分号分隔的环境变量路径整理成每行一个的格式
"""

def format_env_paths(input_file, output_file=None):
    """
    读取包含分号分隔路径的文件，将其格式化为每行一个路径
    
    Args:
        input_file (str): 输入文件路径
        output_file (str, optional): 输出文件路径，如果不指定则在原文件名后加_formatted
    """
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 按分号分割路径
        paths = content.split(';')
        
        # 过滤掉空字符串
        paths = [path.strip() for path in paths if path.strip()]
        
        # 确定输出文件名
        if output_file is None:
            if input_file.endswith('.txt'):
                output_file = input_file.replace('.txt', '_formatted.txt')
            else:
                output_file = input_file + '_formatted.txt'
        
        # 写入格式化后的内容
        with open(output_file, 'w', encoding='utf-8') as f:
            for path in paths:
                f.write(path + '\n')
        
        print(f"✅ 成功处理 {len(paths)} 个路径")
        print(f"📁 输入文件: {input_file}")
        print(f"📄 输出文件: {output_file}")
        
        # 显示前几个路径作为预览
        print("\n📋 前5个路径预览:")
        for i, path in enumerate(paths[:5], 1):
            print(f"  {i}. {path}")
        
        if len(paths) > 5:
            print(f"  ... 还有 {len(paths) - 5} 个路径")
            
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 '{input_file}'")
    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")

def main():
    """主函数"""
    import sys
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print(f"  python {sys.argv[0]} <输入文件> [输出文件]")
        print("\n示例:")
        print(f"  python {sys.argv[0]} try.txt")
        print(f"  python {sys.argv[0]} try.txt formatted_paths.txt")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    format_env_paths(input_file, output_file)

if __name__ == "__main__":
    # 如果直接运行脚本，处理try.txt文件
    format_env_paths("try.txt")
