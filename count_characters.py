#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件字符统计工具
统计文件的字符数、行数等信息
"""

def count_file_stats(filename):
    """
    统计文件的详细信息
    
    Args:
        filename (str): 文件路径
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计各种信息
        total_chars = len(content)
        total_chars_no_spaces = len(content.replace(' ', ''))
        total_chars_no_whitespace = len(''.join(content.split()))
        lines = content.split('\n')
        total_lines = len(lines)
        non_empty_lines = len([line for line in lines if line.strip()])
        total_words = len(content.split())
        
        print(f"📊 文件统计信息: {filename}")
        print("=" * 50)
        print(f"📝 总字符数 (包含所有字符): {total_chars:,}")
        print(f"🔤 字符数 (不含空格): {total_chars_no_spaces:,}")
        print(f"📄 字符数 (不含任何空白字符): {total_chars_no_whitespace:,}")
        print(f"📋 总行数: {total_lines:,}")
        print(f"📄 非空行数: {non_empty_lines:,}")
        print(f"📝 总单词数: {total_words:,}")
        
        # 计算平均每行字符数
        if non_empty_lines > 0:
            avg_chars_per_line = total_chars_no_whitespace / non_empty_lines
            print(f"📊 平均每行字符数 (不含空白): {avg_chars_per_line:.1f}")
        
        return {
            'total_chars': total_chars,
            'total_chars_no_spaces': total_chars_no_spaces,
            'total_chars_no_whitespace': total_chars_no_whitespace,
            'total_lines': total_lines,
            'non_empty_lines': non_empty_lines,
            'total_words': total_words
        }
        
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 '{filename}'")
        return None
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return None

if __name__ == "__main__":
    # 统计 try_formatted.txt 文件
    count_file_stats("try_formatted.txt")
